# Ninecho - Modern Responsive Typecho Theme

一个现代化、响应式的 Typecho 博客主题，专注于用户体验、视觉设计和现代网络标准。

**版本**: 1.0.2
**作者**: Geek0ne
**更新日期**: 2024-08-08

## ✨ 特性

### 🎨 设计系统
- **简约设计**：强调留白和排版的现代化设计
- **深色/浅色模式**：支持系统偏好检测的主题切换
- **响应式设计**：移动优先，支持所有屏幕尺寸
- **流畅动画**：CSS过渡和微交互效果
- **优秀排版**：网络安全字体与备用方案

### 🏠 首页布局
- **多种布局选项**：卡片网格、列表视图、杂志风格
- **特色文章区域**：顶部置顶文章展示
- **丰富侧边栏**：分类/标签云、作者简介、最新评论
- **性能优化**：图片和内容懒加载

### 📖 文章页面
- **阅读体验**：进度条和预估阅读时间
- **智能导航**：长文章自动生成目录
- **代码展示**：语法高亮和复制按钮
- **媒体支持**：图片灯箱/画廊功能
- **社交分享**：微博、微信等分享按钮
- **文章导航**：上一篇/下一篇预览

### 📄 页面模板
- **归档页面**：时间轴风格，按年/月分组
- **分类/标签页面**：网格布局显示
- **友链页面**：有组织的友情链接展示
- **关于页面**：完整 Markdown 支持
- **404页面**：创意错误页面和导航建议

### 🚀 高级功能
- **访客统计**：内置计数器（无需插件）
- **无障碍支持**：WCAG 2.1 AA 合规性
- **性能优化**：懒加载、CSS/JS压缩
- **PWA支持**：离线访问和应用安装
- **主题自定义**：管理面板配置选项

### 🔍 SEO优化
- **语义化HTML5**：结构化标记
- **结构化数据**：自动生成 JSON-LD
- **元标签优化**：Open Graph 和 Twitter Card
- **站点地图**：自动 XML 站点地图生成
- **Core Web Vitals**：性能指标优化

## 📦 安装

1. **下载主题**
   ```bash
   git clone https://github.com/your-username/ninecho.git
   ```

2. **上传主题**
   - 将主题文件夹上传到 `/usr/themes/` 目录
   - 重命名文件夹为 `ninecho`

3. **启用主题**
   - 登录 Typecho 后台
   - 进入 `控制台` → `外观` → `设置外观`
   - 选择 "Ninecho" 并启用

4. **配置主题**
   - 在外观设置页面配置主题选项
   - 设置网站 Logo、颜色、布局等

## 📸 主题预览

主题包含多种格式的预览文件：
- `screenshot.jpg` - 主题预览图（896x504像素，JPEG格式）
- `screenshot.png` - PNG格式预览图（兼容性备选）
- `screenshot.svg` - SVG矢量预览图（自定义设计，可缩放）

这些文件确保主题在 Typecho 后台能够正确显示预览图。

## ⚙️ 配置选项

### 基础设置
- **网站 Logo**：上传自定义 Logo
- **主题色彩**：自定义主色调
- **首页布局**：选择网格、列表或杂志布局
- **侧边栏位置**：左侧或右侧

### 功能设置
- **深色模式**：启用/禁用深色模式切换
- **阅读进度**：显示文章阅读进度条
- **代码高亮**：启用语法高亮
- **图片灯箱**：启用图片放大查看
- **社交分享**：配置分享按钮

### SEO设置
- **网站关键词**：设置默认关键词
- **网站描述**：自定义网站描述
- **统计代码**：添加 Google Analytics 等
- **结构化数据**：启用 JSON-LD 标记

### 高级设置
- **自定义CSS**：添加自定义样式
- **自定义JavaScript**：添加自定义脚本
- **CDN设置**：配置静态资源CDN
- **缓存优化**：启用浏览器缓存

## 🎯 使用指南

### 自定义字段
主题支持以下自定义字段：

#### 文章字段
- `thumbnail`：自定义缩略图
- `description`：SEO描述
- `keywords`：SEO关键词
- `featured`：设为特色文章

#### 页面字段
- `subtitle`：页面副标题
- `template`：页面模板（links、about等）
- `friends`：友链数据（JSON格式）

### 菜单设置
1. 进入 `控制台` → `管理` → `菜单`
2. 添加菜单项，支持：
   - 页面链接
   - 分类链接
   - 自定义链接
   - 下拉子菜单

### 小工具配置
侧边栏支持以下小工具：
- 作者信息卡片
- 最新文章列表
- 分类目录
- 标签云
- 最新评论
- 文章归档
- 友情链接

## 🛠️ 开发

### 文件结构
```
ninecho/
├── assets/
│   ├── css/
│   │   └── style.css
│   ├── js/
│   │   └── main.js
│   └── images/
├── index.php          # 首页模板
├── header.php          # 头部模板
├── footer.php          # 底部模板
├── sidebar.php         # 侧边栏模板
├── post.php           # 文章页模板
├── page.php           # 页面模板
├── archive.php        # 归档页模板
├── category.php       # 分类页模板
├── tag.php           # 标签页模板
├── 404.php           # 404页模板
├── comments.php       # 评论模板
├── functions.php      # 主题函数
├── sw.js             # Service Worker
├── manifest.json     # PWA清单
├── offline.html      # 离线页面
├── sitemap.php       # 站点地图
└── robots.txt        # 爬虫协议
```

### 自定义开发
1. **修改样式**：编辑 `assets/css/style.css`
2. **添加功能**：在 `functions.php` 中添加函数
3. **修改模板**：编辑对应的 PHP 模板文件
4. **添加脚本**：在 `assets/js/main.js` 中添加 JavaScript

## 🔧 故障排除

### 常见问题

**Q: 主题样式不显示？**
A: 检查文件权限，确保 `/assets/` 目录可读

**Q: 图片不显示？**
A: 检查图片路径，确保使用相对路径

**Q: 深色模式不工作？**
A: 清除浏览器缓存，检查 JavaScript 是否正常加载

**Q: 评论功能异常？**
A: 检查 Typecho 评论设置，确保评论功能已启用

### 性能优化建议
1. 启用 Gzip 压缩
2. 配置浏览器缓存
3. 使用 CDN 加速静态资源
4. 优化图片大小和格式
5. 启用 Service Worker 缓存

## 📄 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 🔧 故障排除

### 页面显示空白问题

如果遇到首页或其他页面显示空白，请检查：

1. **主题文件完整性**
   - 确保所有主题文件都已正确上传
   - 检查文件权限是否正确（建议 644）

2. **服务器环境**
   - PHP 版本：推荐 7.4 或更高版本
   - Typecho 版本：推荐 1.1 或更高版本
   - 确保启用了必要的 PHP 扩展（mbstring, gd 等）

3. **主题配置**
   - 在 Typecho 后台确认已正确启用主题
   - 检查主题设置是否正确配置

4. **创建必要页面**
   - 创建「文章分类」页面，自定义字段 `template` 值为 `categories`
   - 创建「文章归档」页面，自定义字段 `template` 值为 `archives`
   - 创建「文章标签」页面，自定义字段 `template` 值为 `tags`
   - 创建「友情链接」页面，自定义字段 `template` 值为 `links`

5. **调试方法**
   - 启用 PHP 错误显示查看具体错误信息
   - 检查服务器错误日志
   - 使用浏览器开发者工具检查网络请求

### 样式显示异常

如果样式显示不正常：

1. 清除浏览器缓存
2. 检查 CSS 文件是否正确加载
3. 确认主题目录结构完整

## 📞 支持

- **文档**：[主题文档](https://example.com/docs)
- **问题反馈**：[GitHub Issues](https://github.com/your-username/ninecho/issues)
- **讨论交流**：[GitHub Discussions](https://github.com/your-username/ninecho/discussions)

---

**Ninecho** - 让您的博客更加现代化！
