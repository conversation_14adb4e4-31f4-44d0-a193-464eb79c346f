/**
 * Service Worker for Ninecho Theme
 * Provides offline functionality and caching
 */

const CACHE_NAME = 'ninecho-v1.0.0';
const STATIC_CACHE = 'static-v1.0.0';
const DYNAMIC_CACHE = 'dynamic-v1.0.0';

// Files to cache immediately
const STATIC_FILES = [
    '/',
    '/assets/css/style.css',
    '/assets/js/main.js',
    '/assets/images/logo.png',
    '/assets/images/default-avatar.png',
    '/manifest.json',
    '/offline.html'
];

// Install event - cache static files
self.addEventListener('install', event => {
    console.log('Service Worker installing...');
    
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('Caching static files');
                return cache.addAll(STATIC_FILES);
            })
            .then(() => {
                return self.skipWaiting();
            })
    );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
    console.log('Service Worker activating...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                            console.log('Deleting old cache:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                return self.clients.claim();
            })
    );
});

// Fetch event - serve from cache, fallback to network
self.addEventListener('fetch', event => {
    const { request } = event;
    const url = new URL(request.url);
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }
    
    // Skip external requests
    if (url.origin !== location.origin) {
        return;
    }
    
    // Handle different types of requests
    if (isStaticAsset(request)) {
        event.respondWith(cacheFirst(request));
    } else if (isHTMLPage(request)) {
        event.respondWith(networkFirst(request));
    } else if (isImage(request)) {
        event.respondWith(cacheFirst(request));
    } else {
        event.respondWith(networkFirst(request));
    }
});

// Cache strategies
async function cacheFirst(request) {
    try {
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(STATIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        console.log('Cache first failed:', error);
        return getOfflineFallback(request);
    }
}

async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        console.log('Network first failed, trying cache:', error);
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }
        return getOfflineFallback(request);
    }
}

// Helper functions
function isStaticAsset(request) {
    return request.url.includes('/assets/') || 
           request.url.includes('.css') || 
           request.url.includes('.js') ||
           request.url.includes('.woff') ||
           request.url.includes('.woff2');
}

function isHTMLPage(request) {
    return request.headers.get('accept').includes('text/html');
}

function isImage(request) {
    return request.headers.get('accept').includes('image/');
}

async function getOfflineFallback(request) {
    if (isHTMLPage(request)) {
        return caches.match('/offline.html');
    }
    
    if (isImage(request)) {
        return caches.match('/assets/images/offline-image.svg');
    }
    
    return new Response('Offline', {
        status: 503,
        statusText: 'Service Unavailable'
    });
}

// Background sync for form submissions
self.addEventListener('sync', event => {
    if (event.tag === 'comment-sync') {
        event.waitUntil(syncComments());
    }
});

async function syncComments() {
    try {
        const db = await openDB();
        const tx = db.transaction(['comments'], 'readonly');
        const store = tx.objectStore('comments');
        const comments = await store.getAll();
        
        for (const comment of comments) {
            try {
                const response = await fetch('/comments', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(comment.data)
                });
                
                if (response.ok) {
                    // Remove from IndexedDB after successful sync
                    const deleteTx = db.transaction(['comments'], 'readwrite');
                    const deleteStore = deleteTx.objectStore('comments');
                    await deleteStore.delete(comment.id);
                }
            } catch (error) {
                console.log('Failed to sync comment:', error);
            }
        }
    } catch (error) {
        console.log('Background sync failed:', error);
    }
}

// Push notifications
self.addEventListener('push', event => {
    if (!event.data) {
        return;
    }
    
    const data = event.data.json();
    const options = {
        body: data.body,
        icon: '/assets/images/icon-192.png',
        badge: '/assets/images/badge-72.png',
        tag: data.tag || 'default',
        data: data.url || '/',
        actions: [
            {
                action: 'view',
                title: '查看',
                icon: '/assets/images/view-icon.png'
            },
            {
                action: 'dismiss',
                title: '忽略',
                icon: '/assets/images/dismiss-icon.png'
            }
        ]
    };
    
    event.waitUntil(
        self.registration.showNotification(data.title, options)
    );
});

// Notification click handler
self.addEventListener('notificationclick', event => {
    event.notification.close();
    
    if (event.action === 'view') {
        event.waitUntil(
            clients.openWindow(event.notification.data)
        );
    }
});

// IndexedDB helper
function openDB() {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('TypechoThemeDB', 1);
        
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
        
        request.onupgradeneeded = event => {
            const db = event.target.result;
            
            if (!db.objectStoreNames.contains('comments')) {
                const store = db.createObjectStore('comments', { keyPath: 'id', autoIncrement: true });
                store.createIndex('timestamp', 'timestamp', { unique: false });
            }
        };
    });
}

// Periodic background sync
self.addEventListener('periodicsync', event => {
    if (event.tag === 'content-sync') {
        event.waitUntil(syncContent());
    }
});

async function syncContent() {
    try {
        // Sync latest posts
        const response = await fetch('/api/posts?latest=true');
        if (response.ok) {
            const posts = await response.json();
            const cache = await caches.open(DYNAMIC_CACHE);
            
            // Cache latest posts
            for (const post of posts) {
                try {
                    const postResponse = await fetch(post.url);
                    if (postResponse.ok) {
                        cache.put(post.url, postResponse);
                    }
                } catch (error) {
                    console.log('Failed to cache post:', post.url, error);
                }
            }
        }
    } catch (error) {
        console.log('Content sync failed:', error);
    }
}

// Share target handler
self.addEventListener('fetch', event => {
    const url = new URL(event.request.url);
    
    if (url.pathname === '/share-target' && event.request.method === 'POST') {
        event.respondWith(handleShareTarget(event.request));
    }
});

async function handleShareTarget(request) {
    const formData = await request.formData();
    const title = formData.get('title') || '';
    const text = formData.get('text') || '';
    const url = formData.get('url') || '';
    
    // Redirect to compose page with shared content
    const composeUrl = `/admin/write-post.php?title=${encodeURIComponent(title)}&content=${encodeURIComponent(text + '\n\n' + url)}`;
    
    return Response.redirect(composeUrl, 302);
}

// Cache management
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'CACHE_URLS') {
        event.waitUntil(
            cacheUrls(event.data.urls)
        );
    }
});

async function cacheUrls(urls) {
    const cache = await caches.open(DYNAMIC_CACHE);
    
    for (const url of urls) {
        try {
            const response = await fetch(url);
            if (response.ok) {
                await cache.put(url, response);
            }
        } catch (error) {
            console.log('Failed to cache URL:', url, error);
        }
    }
}
