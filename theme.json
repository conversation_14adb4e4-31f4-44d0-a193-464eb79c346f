{"name": "<PERSON><PERSON>", "version": "1.0.3", "description": "一个现代化、响应式的 Typecho 博客主题，专注于用户体验、视觉设计和现代网络标准", "author": "Geek0ne", "homepage": "https://github.com/geek0ne/ninecho", "license": "MIT", "keywords": ["typecho", "theme", "responsive", "modern", "blog", "dark-mode", "pwa", "seo"], "features": ["响应式设计", "深色/浅色模式", "PWA 支持", "SEO 优化", "无障碍支持", "性能优化", "多种布局", "社交分享", "代码高亮", "图片灯箱", "阅读进度", "目录导航", "访客统计", "友情链接", "时间轴归档"], "requirements": {"typecho": ">=1.1.0", "php": ">=7.0"}, "support": {"issues": "https://github.com/geek0ne/ninecho/issues", "documentation": "https://github.com/geek0ne/ninecho/wiki", "discussions": "https://github.com/geek0ne/ninecho/discussions"}, "screenshots": ["screenshot-1.png", "screenshot-2.png", "screenshot-3.png"], "demo": "https://ninecho-demo.example.com", "changelog": {"1.0.3": {"date": "2024-08-08", "changes": ["修复页面模板识别机制，支持自定义模板选择", "完善文章详情页面设计和功能", "修复底部备案信息换行显示问题", "优化文章链接和导航功能", "增强页面模板系统的稳定性"]}, "1.0.2": {"date": "2024-08-08", "changes": ["修复页面模板识别问题", "优化文章链接导航", "增强表情包面板宽度和体验", "更新作者信息为 Geek0ne", "修复评论表情包点击功能", "简化文章页面模板结构", "移除页脚 Typecho 版权信息"]}, "1.0.1": {"date": "2024-08-07", "changes": ["修复数据库兼容性问题", "解决 onDuplicate 方法错误", "完善主题后台配置选项", "修复空白页面显示问题", "创建独立页面模板"]}, "1.0.0": {"date": "2024-01-01", "changes": ["初始版本发布", "完整的响应式设计系统", "深色/浅色模式支持", "PWA 功能实现", "SEO 优化完成", "无障碍支持", "性能优化", "多种首页布局", "完整的页面模板", "高级功能集成"]}}}