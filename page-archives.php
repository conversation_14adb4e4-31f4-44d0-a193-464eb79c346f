<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>
<?php
/**
 * 文章归档
 *
 * @package Ninecho
 */

$this->need('header.php');
?>

<main class="main-content">
    <div class="container">
        <div class="content-wrapper">
            <div class="page-main">
                <!-- Breadcrumb -->
                <nav class="breadcrumb" aria-label="面包屑导航">
                    <ol class="breadcrumb-list">
                        <li class="breadcrumb-item">
                            <a href="<?php $this->options->siteUrl(); ?>">首页</a>
                        </li>
                        <li class="breadcrumb-item">
                            <?php $this->title() ?>
                        </li>
                    </ol>
                </nav>

                <!-- Page Header -->
                <header class="page-header">
                    <h1 class="page-title">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                            <line x1="16" y1="2" x2="16" y2="6"></line>
                            <line x1="8" y1="2" x2="8" y2="6"></line>
                            <line x1="3" y1="10" x2="21" y2="10"></line>
                        </svg>
                        <?php $this->title() ?>
                    </h1>
                    <?php if ($this->fields->subtitle): ?>
                        <p class="page-subtitle"><?php $this->fields->subtitle(); ?></p>
                    <?php endif; ?>
                    <div class="page-meta">
                        <span class="posts-count">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                <polyline points="14,2 14,8 20,8"></polyline>
                            </svg>
                            <?php 
                            Typecho_Widget::widget('Widget_Stat')->to($stat);
                            echo "共 {$stat->publishedPostsNum} 篇文章";
                            ?>
                        </span>
                        <time datetime="<?php $this->date('c'); ?>" class="page-date">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12,6 12,12 16,14"></polyline>
                            </svg>
                            最后更新：<?php $this->date('Y-m-d'); ?>
                        </time>
                    </div>
                </header>

                <!-- Page Content -->
                <article class="page-content">
                    <div class="page-body">
                        <?php $this->content(); ?>
                    </div>

                    <!-- Archive Statistics -->
                    <div class="archive-stats">
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                        <polyline points="14,2 14,8 20,8"></polyline>
                                    </svg>
                                </div>
                                <div class="stat-info">
                                    <span class="stat-number"><?php echo $stat->publishedPostsNum; ?></span>
                                    <span class="stat-label">文章总数</span>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                                    </svg>
                                </div>
                                <div class="stat-info">
                                    <span class="stat-number"><?php echo $stat->publishedCommentsNum; ?></span>
                                    <span class="stat-label">评论总数</span>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
                                    </svg>
                                </div>
                                <div class="stat-info">
                                    <span class="stat-number">
                                        <?php 
                                        $this->widget('Widget_Metas_Category_List')->to($categoryCount);
                                        $count = 0;
                                        while($categoryCount->next()) { $count++; }
                                        echo $count;
                                        ?>
                                    </span>
                                    <span class="stat-label">分类数量</span>
                                </div>
                            </div>
                            <div class="stat-card">
                                <div class="stat-icon">
                                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path>
                                        <line x1="7" y1="7" x2="7.01" y2="7"></line>
                                    </svg>
                                </div>
                                <div class="stat-info">
                                    <span class="stat-number">
                                        <?php 
                                        $this->widget('Widget_Metas_Tag_Cloud')->to($tagCount);
                                        $tagNum = 0;
                                        while($tagCount->next()) { $tagNum++; }
                                        echo $tagNum;
                                        ?>
                                    </span>
                                    <span class="stat-label">标签数量</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Timeline Archive -->
                    <div class="timeline-archive">
                        <h2 class="archive-section-title">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12,6 12,12 16,14"></polyline>
                            </svg>
                            时间轴归档
                        </h2>
                        
                        <div class="timeline-container">
                            <?php 
                            $this->widget('Widget_Contents_Post_Recent', 'pageSize=1000')->to($posts);
                            $currentYear = '';
                            $currentMonth = '';
                            $yearPosts = array();
                            
                            // 按年月分组
                            while($posts->next()) {
                                $year = date('Y', $posts->created);
                                $month = date('n', $posts->created);
                                $yearPosts[$year][$month][] = array(
                                    'title' => $posts->title,
                                    'permalink' => $posts->permalink,
                                    'created' => $posts->created,
                                    'category' => $posts->category,
                                    'commentsNum' => $posts->commentsNum
                                );
                            }
                            
                            // 按年份倒序排列
                            krsort($yearPosts);
                            
                            foreach ($yearPosts as $year => $months):
                            ?>
                                <div class="timeline-year">
                                    <h3 class="year-title">
                                        <span class="year-number"><?php echo $year; ?></span>
                                        <span class="year-count">
                                            <?php 
                                            $yearCount = 0;
                                            foreach ($months as $monthPosts) {
                                                $yearCount += count($monthPosts);
                                            }
                                            echo "({$yearCount} 篇)";
                                            ?>
                                        </span>
                                    </h3>
                                    <div class="year-content">
                                        <?php 
                                        // 按月份倒序排列
                                        krsort($months);
                                        foreach ($months as $month => $monthPosts):
                                        ?>
                                            <div class="timeline-month">
                                                <h4 class="month-title">
                                                    <?php echo $month; ?>月
                                                    <span class="month-count">(<?php echo count($monthPosts); ?> 篇)</span>
                                                </h4>
                                                <div class="month-posts">
                                                    <?php foreach ($monthPosts as $post): ?>
                                                        <article class="timeline-post">
                                                            <div class="timeline-date">
                                                                <span class="day"><?php echo date('d', $post['created']); ?></span>
                                                            </div>
                                                            <div class="timeline-content">
                                                                <h5 class="timeline-title">
                                                                    <a href="<?php echo $post['permalink']; ?>" title="<?php echo $post['title']; ?>">
                                                                        <?php echo $post['title']; ?>
                                                                    </a>
                                                                </h5>
                                                                <div class="timeline-meta">
                                                                    <span class="post-category">
                                                                        <?php echo $post['category']; ?>
                                                                    </span>
                                                                    <?php if ($post['commentsNum'] > 0): ?>
                                                                        <span class="meta-separator">·</span>
                                                                        <span class="post-comments">
                                                                            <?php echo $post['commentsNum']; ?> 条评论
                                                                        </span>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                        </article>
                                                    <?php endforeach; ?>
                                                </div>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </article>

                <!-- Comments -->
                <?php $this->need('comments.php'); ?>
            </div>
            
            <!-- Sidebar -->
            <?php $this->need('sidebar.php'); ?>
        </div>
    </div>
</main>

<?php $this->need('footer.php'); ?>
