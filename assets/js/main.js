/**
 * Modern Responsive Typecho Theme
 * Main JavaScript File
 */

(function() {
    'use strict';

    // Theme Management
    class ThemeManager {
        constructor() {
            this.themeToggle = document.querySelector('.theme-toggle');
            this.currentTheme = localStorage.getItem('theme') || 'auto';
            this.init();
        }

        init() {
            this.setTheme(this.currentTheme);
            if (this.themeToggle) {
                this.themeToggle.addEventListener('click', () => this.toggleTheme());
            }
        }

        setTheme(theme) {
            document.documentElement.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            this.currentTheme = theme;
        }

        toggleTheme() {
            const themes = ['light', 'dark', 'auto'];
            const currentIndex = themes.indexOf(this.currentTheme);
            const nextIndex = (currentIndex + 1) % themes.length;
            this.setTheme(themes[nextIndex]);
        }
    }

    // Search Functionality
    class SearchManager {
        constructor() {
            this.searchToggle = document.querySelector('.search-toggle');
            this.searchOverlay = document.querySelector('.search-overlay');
            this.searchClose = document.querySelector('.search-close');
            this.searchInput = document.querySelector('#search-input');
            this.init();
        }

        init() {
            if (this.searchToggle) {
                this.searchToggle.addEventListener('click', () => this.openSearch());
            }
            if (this.searchClose) {
                this.searchClose.addEventListener('click', () => this.closeSearch());
            }
            if (this.searchOverlay) {
                this.searchOverlay.addEventListener('click', (e) => {
                    if (e.target === this.searchOverlay) {
                        this.closeSearch();
                    }
                });
            }
            
            // ESC key to close search
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape' && this.searchOverlay.classList.contains('active')) {
                    this.closeSearch();
                }
            });
        }

        openSearch() {
            this.searchOverlay.classList.add('active');
            this.searchOverlay.setAttribute('aria-hidden', 'false');
            this.searchToggle.setAttribute('aria-expanded', 'true');
            if (this.searchInput) {
                setTimeout(() => this.searchInput.focus(), 100);
            }
        }

        closeSearch() {
            this.searchOverlay.classList.remove('active');
            this.searchOverlay.setAttribute('aria-hidden', 'true');
            this.searchToggle.setAttribute('aria-expanded', 'false');
        }
    }

    // Mobile Navigation
    class NavigationManager {
        constructor() {
            this.menuToggle = document.querySelector('.menu-toggle');
            this.navMenu = document.querySelector('.nav-menu');
            this.init();
        }

        init() {
            if (this.menuToggle) {
                this.menuToggle.addEventListener('click', () => this.toggleMenu());
            }

            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!this.menuToggle.contains(e.target) && !this.navMenu.contains(e.target)) {
                    this.closeMenu();
                }
            });

            // Close menu on window resize
            window.addEventListener('resize', () => {
                if (window.innerWidth > 767) {
                    this.closeMenu();
                }
            });
        }

        toggleMenu() {
            const isExpanded = this.menuToggle.getAttribute('aria-expanded') === 'true';
            if (isExpanded) {
                this.closeMenu();
            } else {
                this.openMenu();
            }
        }

        openMenu() {
            this.navMenu.classList.add('active');
            this.menuToggle.setAttribute('aria-expanded', 'true');
        }

        closeMenu() {
            this.navMenu.classList.remove('active');
            this.menuToggle.setAttribute('aria-expanded', 'false');
        }
    }

    // Back to Top Button
    class BackToTopManager {
        constructor() {
            this.backToTopBtn = document.querySelector('.back-to-top');
            this.init();
        }

        init() {
            if (this.backToTopBtn) {
                window.addEventListener('scroll', () => this.handleScroll());
                this.backToTopBtn.addEventListener('click', () => this.scrollToTop());
            }
        }

        handleScroll() {
            if (window.pageYOffset > 300) {
                this.backToTopBtn.classList.add('visible');
            } else {
                this.backToTopBtn.classList.remove('visible');
            }
        }

        scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
    }

    // Reading Progress Bar
    class ReadingProgressManager {
        constructor() {
            this.progressBar = document.querySelector('#reading-progress-bar');
            this.init();
        }

        init() {
            if (this.progressBar) {
                window.addEventListener('scroll', () => this.updateProgress());
            }
        }

        updateProgress() {
            const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
            const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
            const scrolled = (winScroll / height) * 100;
            this.progressBar.style.width = scrolled + '%';
        }
    }

    // Table of Contents
    class TOCManager {
        constructor() {
            this.tocContainer = document.querySelector('#table-of-contents');
            this.tocNav = document.querySelector('#toc-nav');
            this.tocToggle = document.querySelector('.toc-toggle');
            this.postBody = document.querySelector('.post-body');
            this.init();
        }

        init() {
            if (this.tocContainer && this.postBody) {
                this.generateTOC();
                if (this.tocToggle) {
                    this.tocToggle.addEventListener('click', () => this.toggleTOC());
                }
            }
        }

        generateTOC() {
            const headings = this.postBody.querySelectorAll('h1, h2, h3, h4, h5, h6');
            if (headings.length === 0) {
                this.tocContainer.style.display = 'none';
                return;
            }

            const tocList = document.createElement('ul');
            tocList.className = 'toc-list';

            headings.forEach((heading, index) => {
                const id = `heading-${index}`;
                heading.id = id;

                const listItem = document.createElement('li');
                listItem.className = `toc-item toc-${heading.tagName.toLowerCase()}`;

                const link = document.createElement('a');
                link.href = `#${id}`;
                link.textContent = heading.textContent;
                link.className = 'toc-link';

                listItem.appendChild(link);
                tocList.appendChild(listItem);

                // Smooth scroll to heading
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    heading.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                });
            });

            this.tocNav.appendChild(tocList);
        }

        toggleTOC() {
            this.tocNav.classList.toggle('collapsed');
            const isCollapsed = this.tocNav.classList.contains('collapsed');
            this.tocToggle.setAttribute('aria-expanded', !isCollapsed);
        }
    }

    // Lazy Loading Images
    class LazyLoadManager {
        constructor() {
            this.images = document.querySelectorAll('img[loading="lazy"]');
            this.init();
        }

        init() {
            if ('IntersectionObserver' in window) {
                this.observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src || img.src;
                            img.classList.add('loaded');
                            this.observer.unobserve(img);
                        }
                    });
                });

                this.images.forEach(img => {
                    this.observer.observe(img);
                });
            }
        }
    }

    // Social Share
    class SocialShareManager {
        constructor() {
            this.shareButtons = document.querySelectorAll('.share-btn');
            this.init();
        }

        init() {
            this.shareButtons.forEach(btn => {
                btn.addEventListener('click', (e) => this.handleShare(e));
            });
        }

        handleShare(e) {
            e.preventDefault();
            const btn = e.currentTarget;
            const url = btn.dataset.url;
            const title = btn.dataset.title;

            if (btn.classList.contains('weibo-share')) {
                this.shareToWeibo(url, title);
            } else if (btn.classList.contains('wechat-share')) {
                this.shareToWechat(url, title);
            } else if (btn.classList.contains('qq-share')) {
                this.shareToQQ(url, title);
            } else if (btn.classList.contains('copy-link')) {
                this.copyLink(url);
            }
        }

        shareToWeibo(url, title) {
            const shareUrl = `https://service.weibo.com/share/share.php?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}`;
            window.open(shareUrl, '_blank', 'width=600,height=400');
        }

        shareToWechat(url, title) {
            // Generate QR code for WeChat sharing
            this.showQRCode(url, title);
        }

        shareToQQ(url, title) {
            const shareUrl = `https://connect.qq.com/widget/shareqq/index.html?url=${encodeURIComponent(url)}&title=${encodeURIComponent(title)}`;
            window.open(shareUrl, '_blank', 'width=600,height=400');
        }

        copyLink(url) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(url).then(() => {
                    this.showToast('链接已复制到剪贴板');
                });
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = url;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                this.showToast('链接已复制到剪贴板');
            }
        }

        showQRCode(url, title) {
            // This would typically integrate with a QR code library
            this.showToast('请使用微信扫一扫分享');
        }

        showToast(message) {
            const toast = document.createElement('div');
            toast.className = 'toast';
            toast.textContent = message;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--color-primary);
                color: var(--color-text-inverse);
                padding: 12px 24px;
                border-radius: 6px;
                z-index: 10000;
                animation: fadeInUp 0.3s ease-out;
            `;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.animation = 'fadeOut 0.3s ease-out forwards';
                setTimeout(() => {
                    document.body.removeChild(toast);
                }, 300);
            }, 3000);
        }
    }

    // Performance Monitor
    class PerformanceManager {
        constructor() {
            this.init();
        }

        init() {
            // Monitor Core Web Vitals
            this.monitorCoreWebVitals();

            // Preload critical resources
            this.preloadCriticalResources();

            // Optimize images
            this.optimizeImages();
        }

        monitorCoreWebVitals() {
            // Largest Contentful Paint (LCP)
            new PerformanceObserver((entryList) => {
                for (const entry of entryList.getEntries()) {
                    console.log('LCP:', entry.startTime);
                }
            }).observe({entryTypes: ['largest-contentful-paint']});

            // First Input Delay (FID)
            new PerformanceObserver((entryList) => {
                for (const entry of entryList.getEntries()) {
                    console.log('FID:', entry.processingStart - entry.startTime);
                }
            }).observe({entryTypes: ['first-input']});

            // Cumulative Layout Shift (CLS)
            let clsValue = 0;
            new PerformanceObserver((entryList) => {
                for (const entry of entryList.getEntries()) {
                    if (!entry.hadRecentInput) {
                        clsValue += entry.value;
                        console.log('CLS:', clsValue);
                    }
                }
            }).observe({entryTypes: ['layout-shift']});
        }

        preloadCriticalResources() {
            // Preload critical CSS
            const criticalCSS = document.querySelector('link[rel="stylesheet"]');
            if (criticalCSS) {
                const preloadLink = document.createElement('link');
                preloadLink.rel = 'preload';
                preloadLink.as = 'style';
                preloadLink.href = criticalCSS.href;
                document.head.appendChild(preloadLink);
            }

            // Preload fonts
            const fonts = [
                '/assets/fonts/main.woff2',
                '/assets/fonts/main-bold.woff2'
            ];

            fonts.forEach(font => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.as = 'font';
                link.type = 'font/woff2';
                link.crossOrigin = 'anonymous';
                link.href = font;
                document.head.appendChild(link);
            });
        }

        optimizeImages() {
            // Add loading="lazy" to images below the fold
            const images = document.querySelectorAll('img:not([loading])');
            images.forEach((img, index) => {
                if (index > 2) { // Skip first 3 images (likely above fold)
                    img.loading = 'lazy';
                }
            });

            // Convert images to WebP if supported
            if (this.supportsWebP()) {
                const images = document.querySelectorAll('img[data-src]');
                images.forEach(img => {
                    const src = img.dataset.src;
                    if (src && !src.includes('.webp')) {
                        img.dataset.src = src.replace(/\.(jpg|jpeg|png)$/i, '.webp');
                    }
                });
            }
        }

        supportsWebP() {
            const canvas = document.createElement('canvas');
            canvas.width = 1;
            canvas.height = 1;
            return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
        }
    }

    // Accessibility Manager
    class AccessibilityManager {
        constructor() {
            this.init();
        }

        init() {
            this.setupKeyboardNavigation();
            this.setupFocusManagement();
            this.setupScreenReaderSupport();
            this.setupReducedMotion();
        }

        setupKeyboardNavigation() {
            // Skip to main content
            const skipLink = document.querySelector('.skip-link');
            if (skipLink) {
                skipLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    const mainContent = document.querySelector('main');
                    if (mainContent) {
                        mainContent.focus();
                        mainContent.scrollIntoView();
                    }
                });
            }

            // Escape key handlers
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Escape') {
                    // Close search overlay
                    const searchOverlay = document.querySelector('.search-overlay.active');
                    if (searchOverlay) {
                        searchOverlay.classList.remove('active');
                    }

                    // Close mobile menu
                    const mobileMenu = document.querySelector('.nav-menu.active');
                    if (mobileMenu) {
                        mobileMenu.classList.remove('active');
                    }
                }
            });

            // Tab navigation for dropdowns
            const dropdowns = document.querySelectorAll('[aria-haspopup="true"]');
            dropdowns.forEach(dropdown => {
                dropdown.addEventListener('keydown', (e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        dropdown.click();
                    }
                });
            });
        }

        setupFocusManagement() {
            // Focus trap for modals
            const modals = document.querySelectorAll('[role="dialog"]');
            modals.forEach(modal => {
                const focusableElements = modal.querySelectorAll(
                    'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
                );

                if (focusableElements.length > 0) {
                    const firstElement = focusableElements[0];
                    const lastElement = focusableElements[focusableElements.length - 1];

                    modal.addEventListener('keydown', (e) => {
                        if (e.key === 'Tab') {
                            if (e.shiftKey) {
                                if (document.activeElement === firstElement) {
                                    e.preventDefault();
                                    lastElement.focus();
                                }
                            } else {
                                if (document.activeElement === lastElement) {
                                    e.preventDefault();
                                    firstElement.focus();
                                }
                            }
                        }
                    });
                }
            });

            // Focus indicators
            document.addEventListener('keydown', (e) => {
                if (e.key === 'Tab') {
                    document.body.classList.add('keyboard-navigation');
                }
            });

            document.addEventListener('mousedown', () => {
                document.body.classList.remove('keyboard-navigation');
            });
        }

        setupScreenReaderSupport() {
            // Live regions for dynamic content
            const liveRegion = document.createElement('div');
            liveRegion.setAttribute('aria-live', 'polite');
            liveRegion.setAttribute('aria-atomic', 'true');
            liveRegion.className = 'screen-reader-text';
            liveRegion.id = 'live-region';
            document.body.appendChild(liveRegion);

            // Announce page changes
            this.announcePageChange = (message) => {
                liveRegion.textContent = message;
                setTimeout(() => {
                    liveRegion.textContent = '';
                }, 1000);
            };

            // Update ARIA labels dynamically
            const updateAriaLabels = () => {
                const buttons = document.querySelectorAll('button[aria-expanded]');
                buttons.forEach(button => {
                    const expanded = button.getAttribute('aria-expanded') === 'true';
                    const label = button.getAttribute('aria-label');
                    if (label) {
                        button.setAttribute('aria-label',
                            label.replace(/(展开|收起)/, expanded ? '收起' : '展开')
                        );
                    }
                });
            };

            // Observe DOM changes
            const observer = new MutationObserver(updateAriaLabels);
            observer.observe(document.body, {
                attributes: true,
                attributeFilter: ['aria-expanded'],
                subtree: true
            });
        }

        setupReducedMotion() {
            // Respect user's motion preferences
            const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');

            const handleReducedMotion = (e) => {
                if (e.matches) {
                    document.body.classList.add('reduced-motion');
                } else {
                    document.body.classList.remove('reduced-motion');
                }
            };

            prefersReducedMotion.addListener(handleReducedMotion);
            handleReducedMotion(prefersReducedMotion);
        }
    }

    // Service Worker Manager
    class ServiceWorkerManager {
        constructor() {
            this.init();
        }

        init() {
            if ('serviceWorker' in navigator) {
                this.registerServiceWorker();
            }
        }

        async registerServiceWorker() {
            try {
                const registration = await navigator.serviceWorker.register('/sw.js');
                console.log('Service Worker registered:', registration);

                // Handle updates
                registration.addEventListener('updatefound', () => {
                    const newWorker = registration.installing;
                    newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            this.showUpdateNotification();
                        }
                    });
                });
            } catch (error) {
                console.log('Service Worker registration failed:', error);
            }
        }

        showUpdateNotification() {
            const notification = document.createElement('div');
            notification.className = 'update-notification';
            notification.innerHTML = `
                <div class="notification-content">
                    <p>网站有新版本可用</p>
                    <button onclick="window.location.reload()" class="btn btn-primary">更新</button>
                    <button onclick="this.parentElement.parentElement.remove()" class="btn btn-secondary">稍后</button>
                </div>
            `;
            document.body.appendChild(notification);
        }
    }

    // Emoji Picker Manager
    class EmojiManager {
        constructor() {
            this.emojiBtn = document.querySelector('.emoji-btn');
            this.emojiPanel = document.querySelector('.emoji-panel');
            this.emojiItems = document.querySelectorAll('.emoji-item');
            this.textarea = document.querySelector('#textarea');
            this.init();
        }

        init() {
            if (this.emojiBtn && this.emojiPanel) {
                this.emojiBtn.addEventListener('click', () => this.toggleEmojiPanel());

                // Close emoji panel when clicking outside
                document.addEventListener('click', (e) => {
                    if (!this.emojiBtn.contains(e.target) && !this.emojiPanel.contains(e.target)) {
                        this.closeEmojiPanel();
                    }
                });
            }

            // Handle emoji clicks
            this.emojiItems.forEach(emoji => {
                emoji.addEventListener('click', () => this.insertEmoji(emoji.textContent));
            });
        }

        toggleEmojiPanel() {
            this.emojiPanel.classList.toggle('active');
        }

        closeEmojiPanel() {
            this.emojiPanel.classList.remove('active');
        }

        insertEmoji(emoji) {
            if (this.textarea) {
                const start = this.textarea.selectionStart;
                const end = this.textarea.selectionEnd;
                const text = this.textarea.value;

                this.textarea.value = text.substring(0, start) + emoji + text.substring(end);
                this.textarea.selectionStart = this.textarea.selectionEnd = start + emoji.length;
                this.textarea.focus();

                // Trigger input event for character count
                this.textarea.dispatchEvent(new Event('input'));
            }
            this.closeEmojiPanel();
        }
    }

    // Initialize all managers when DOM is ready
    document.addEventListener('DOMContentLoaded', function() {
        new ThemeManager();
        new SearchManager();
        new NavigationManager();
        new BackToTopManager();
        new ReadingProgressManager();
        new TOCManager();
        new LazyLoadManager();
        new SocialShareManager();
        new PerformanceManager();
        new AccessibilityManager();
        new ServiceWorkerManager();
        new EmojiManager();
    });

})();
