<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>
<?php
/**
 * 文章标签
 *
 * @package Ninecho
 */

$this->need('header.php');
?>

<main class="main-content">
    <div class="container">
        <div class="content-wrapper">
            <div class="page-main">
                <!-- Breadcrumb -->
                <nav class="breadcrumb" aria-label="面包屑导航">
                    <ol class="breadcrumb-list">
                        <li class="breadcrumb-item">
                            <a href="<?php $this->options->siteUrl(); ?>">首页</a>
                        </li>
                        <li class="breadcrumb-item">
                            <?php $this->title() ?>
                        </li>
                    </ol>
                </nav>

                <!-- Page Header -->
                <header class="page-header">
                    <h1 class="page-title">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path>
                            <line x1="7" y1="7" x2="7.01" y2="7"></line>
                        </svg>
                        <?php $this->title() ?>
                    </h1>
                    <?php if ($this->fields->subtitle): ?>
                        <p class="page-subtitle"><?php $this->fields->subtitle(); ?></p>
                    <?php endif; ?>
                    <div class="page-meta">
                        <span class="tags-count">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path>
                                <line x1="7" y1="7" x2="7.01" y2="7"></line>
                            </svg>
                            <?php 
                            $this->widget('Widget_Metas_Tag_Cloud')->to($tagCount);
                            $count = 0;
                            while($tagCount->next()) { $count++; }
                            echo "共 {$count} 个标签";
                            ?>
                        </span>
                        <time datetime="<?php $this->date('c'); ?>" class="page-date">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12,6 12,12 16,14"></polyline>
                            </svg>
                            最后更新：<?php $this->date('Y-m-d'); ?>
                        </time>
                    </div>
                </header>

                <!-- Page Content -->
                <article class="page-content">
                    <div class="page-body">
                        <?php $this->content(); ?>
                    </div>

                    <!-- Tags Cloud -->
                    <div class="tags-cloud-section">
                        <h2 class="section-title">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path>
                                <line x1="7" y1="7" x2="7.01" y2="7"></line>
                            </svg>
                            标签云
                        </h2>
                        <div class="tags-cloud">
                            <?php $this->widget('Widget_Metas_Tag_Cloud', 'sort=count&ignoreZeroCount=1&desc=1&limit=100')->to($tags); ?>
                            <?php if($tags->have()): ?>
                                <?php while($tags->next()): ?>
                                    <a href="<?php $tags->permalink(); ?>" 
                                       class="tag-cloud-item" 
                                       style="font-size: <?php echo 12 + min($tags->count * 2, 20); ?>px;"
                                       title="<?php $tags->name(); ?> (<?php $tags->count(); ?> 篇文章)">
                                        <?php $tags->name(); ?>
                                        <span class="tag-count"><?php $tags->count(); ?></span>
                                    </a>
                                <?php endwhile; ?>
                            <?php else: ?>
                                <div class="no-tags">
                                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <line x1="4.93" y1="4.93" x2="19.07" y2="19.07"></line>
                                    </svg>
                                    <p>暂无标签</p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Tags List -->
                    <div class="tags-list-section">
                        <h2 class="section-title">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <line x1="8" y1="6" x2="21" y2="6"></line>
                                <line x1="8" y1="12" x2="21" y2="12"></line>
                                <line x1="8" y1="18" x2="21" y2="18"></line>
                                <line x1="3" y1="6" x2="3.01" y2="6"></line>
                                <line x1="3" y1="12" x2="3.01" y2="12"></line>
                                <line x1="3" y1="18" x2="3.01" y2="18"></line>
                            </svg>
                            标签列表
                        </h2>
                        <div class="tags-grid">
                            <?php $this->widget('Widget_Metas_Tag_Cloud', 'sort=count&ignoreZeroCount=1&desc=1&limit=100')->to($tagsList); ?>
                            <?php if($tagsList->have()): ?>
                                <?php while($tagsList->next()): ?>
                                    <div class="tag-card">
                                        <div class="tag-icon">
                                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                                <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path>
                                                <line x1="7" y1="7" x2="7.01" y2="7"></line>
                                            </svg>
                                        </div>
                                        <div class="tag-info">
                                            <h3 class="tag-name">
                                                <a href="<?php $tagsList->permalink(); ?>" title="查看标签：<?php $tagsList->name(); ?>">
                                                    <?php $tagsList->name(); ?>
                                                </a>
                                            </h3>
                                            <div class="tag-meta">
                                                <span class="tag-count">
                                                    <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                                        <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                                        <polyline points="14,2 14,8 20,8"></polyline>
                                                        <line x1="16" y1="13" x2="8" y2="13"></line>
                                                        <line x1="16" y1="17" x2="8" y2="17"></line>
                                                        <polyline points="10,9 9,9 8,9"></polyline>
                                                    </svg>
                                                    <?php $tagsList->count(); ?> 篇文章
                                                </span>
                                            </div>
                                        </div>
                                        <div class="tag-actions">
                                            <a href="<?php $tagsList->permalink(); ?>" class="view-tag-btn" title="查看标签">
                                                <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                                    <circle cx="12" cy="12" r="3"></circle>
                                                </svg>
                                            </a>
                                        </div>
                                    </div>
                                <?php endwhile; ?>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Tag Statistics -->
                    <div class="tag-stats">
                        <h2 class="stats-title">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <line x1="18" y1="20" x2="18" y2="10"></line>
                                <line x1="12" y1="20" x2="12" y2="4"></line>
                                <line x1="6" y1="20" x2="6" y2="14"></line>
                            </svg>
                            标签统计
                        </h2>
                        <div class="stats-grid">
                            <?php 
                            $this->widget('Widget_Metas_Tag_Cloud', 'sort=count&ignoreZeroCount=1&desc=1&limit=10')->to($statsTagsList);
                            $totalTagPosts = 0;
                            $tagsData = array();
                            while($statsTagsList->next()) {
                                $totalTagPosts += $statsTagsList->count;
                                $tagsData[] = array(
                                    'name' => $statsTagsList->name,
                                    'count' => $statsTagsList->count,
                                    'permalink' => $statsTagsList->permalink
                                );
                            }
                            
                            foreach ($tagsData as $index => $tag):
                                $percentage = $totalTagPosts > 0 ? round(($tag['count'] / $totalTagPosts) * 100, 1) : 0;
                            ?>
                                <div class="stat-item">
                                    <div class="stat-header">
                                        <span class="stat-rank">#<?php echo $index + 1; ?></span>
                                        <a href="<?php echo $tag['permalink']; ?>" class="stat-name">
                                            <?php echo $tag['name']; ?>
                                        </a>
                                    </div>
                                    <div class="stat-bar">
                                        <div class="stat-progress" style="width: <?php echo $percentage; ?>%"></div>
                                    </div>
                                    <div class="stat-info">
                                        <span class="stat-count"><?php echo $tag['count']; ?> 篇</span>
                                        <span class="stat-percentage"><?php echo $percentage; ?>%</span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </article>

                <!-- Comments -->
                <?php $this->need('comments.php'); ?>
            </div>
            
            <!-- Sidebar -->
            <?php $this->need('sidebar.php'); ?>
        </div>
    </div>
</main>

<?php $this->need('footer.php'); ?>
