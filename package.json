{"name": "ninecho", "version": "1.0.0", "description": "一个现代化、响应式的 Typecho 博客主题", "main": "index.php", "scripts": {"dev": "echo 'Development mode - watch for changes'", "build": "echo 'Build production assets'", "lint": "echo 'Lint CSS and JS files'", "test": "echo 'Run theme tests'"}, "keywords": ["typecho", "theme", "responsive", "modern", "blog", "dark-mode", "pwa", "seo"], "author": "Your Name", "license": "MIT", "homepage": "https://github.com/your-username/ninecho", "repository": {"type": "git", "url": "https://github.com/your-username/ninecho.git"}, "bugs": {"url": "https://github.com/your-username/ninecho/issues"}, "engines": {"node": ">=14.0.0"}, "devDependencies": {"autoprefixer": "^10.4.0", "cssnano": "^5.1.0", "postcss": "^8.4.0", "postcss-cli": "^9.1.0", "terser": "^5.10.0"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "files": ["*.php", "assets/", "*.html", "*.json", "*.txt", "*.md"]}