<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>
<?php $this->need('header.php'); ?>

<main class="main-content">
    <div class="container">
        <div class="content-wrapper">
            <div class="post-main">
                <!-- Breadcrumb -->
                <nav class="breadcrumb" aria-label="面包屑导航">
                    <ol class="breadcrumb-list">
                        <li class="breadcrumb-item">
                            <a href="<?php $this->options->siteUrl(); ?>">首页</a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?php $this->options->siteUrl(); ?>">文章</a>
                        </li>
                        <li class="breadcrumb-item">
                            <?php $this->title() ?>
                        </li>
                    </ol>
                </nav>

                <!-- Post Header -->
                <header class="post-header">
                    <h1 class="post-title" itemprop="headline"><?php $this->title() ?></h1>

                    <div class="post-meta">
                        <time class="post-date" datetime="<?php $this->date('c'); ?>" itemprop="datePublished">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12,6 12,12 16,14"></polyline>
                            </svg>
                            <?php $this->date('Y-m-d'); ?>
                        </time>

                        <span class="meta-separator">·</span>

                        <span class="post-author" itemprop="author">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                            <?php $this->author(); ?>
                        </span>

                        <span class="meta-separator">·</span>

                        <span class="post-category">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
                            </svg>
                            <?php $this->category(','); ?>
                        </span>

                        <?php if ($this->commentsNum > 0): ?>
                            <span class="meta-separator">·</span>
                            <span class="post-comments">
                                <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                    <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                                </svg>
                                <?php $this->commentsNum('%d 条评论', '暂无评论', '%d 条评论'); ?>
                            </span>
                        <?php endif; ?>
                    </div>
                </header>

                <!-- Post Content -->
                <article class="post-content" itemprop="articleBody">
                    <?php $this->content(); ?>
                </article>

                <!-- Post Tags -->
                <?php if ($this->tags): ?>
                    <div class="post-tags">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path>
                            <line x1="7" y1="7" x2="7.01" y2="7"></line>
                        </svg>
                        <?php $this->tags(' ', true, 'none'); ?>
                    </div>
                <?php endif; ?>

                <!-- Post Navigation -->
                <nav class="post-navigation">
                    <div class="nav-previous">
                        <?php $this->thePrev('%s', '没有了'); ?>
                    </div>
                    <div class="nav-next">
                        <?php $this->theNext('%s', '没有了'); ?>
                    </div>
                </nav>

                <!-- Comments -->
                <?php $this->need('comments.php'); ?>
            </div>

            <!-- Sidebar -->
            <?php $this->need('sidebar.php'); ?>
        </div>
    </div>
</main>

<?php $this->need('footer.php'); ?>
