<svg width="1200" height="900" viewBox="0 0 1200 900" xmlns="http://www.w3.org/2000/svg">
  <!-- Background -->
  <rect width="1200" height="900" fill="#f8fafc"/>
  
  <!-- Header -->
  <rect width="1200" height="80" fill="#ffffff" stroke="#e2e8f0" stroke-width="1"/>
  
  <!-- Logo area -->
  <rect x="40" y="20" width="120" height="40" fill="#2563eb" rx="4"/>
  <text x="100" y="45" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Ninecho</text>
  
  <!-- Navigation -->
  <text x="200" y="45" fill="#1e293b" font-family="Arial, sans-serif" font-size="14">首页</text>
  <text x="250" y="45" fill="#64748b" font-family="Arial, sans-serif" font-size="14">分类</text>
  <text x="300" y="45" fill="#64748b" font-family="Arial, sans-serif" font-size="14">归档</text>
  <text x="350" y="45" fill="#64748b" font-family="Arial, sans-serif" font-size="14">标签</text>
  
  <!-- Search and theme toggle -->
  <circle cx="1120" cy="40" r="16" fill="#f1f5f9" stroke="#e2e8f0"/>
  <circle cx="1160" cy="40" r="16" fill="#f1f5f9" stroke="#e2e8f0"/>
  
  <!-- Main content area -->
  <rect x="40" y="120" width="800" height="720" fill="#ffffff" stroke="#e2e8f0" stroke-width="1" rx="8"/>
  
  <!-- Article cards -->
  <rect x="60" y="140" width="360" height="200" fill="#ffffff" stroke="#e2e8f0" stroke-width="1" rx="6"/>
  <rect x="60" y="150" width="360" height="120" fill="#f1f5f9" rx="4"/>
  <text x="80" y="280" fill="#1e293b" font-family="Arial, sans-serif" font-size="16" font-weight="bold">现代化博客主题</text>
  <text x="80" y="300" fill="#64748b" font-family="Arial, sans-serif" font-size="12">响应式设计，支持深色模式</text>
  <text x="80" y="320" fill="#94a3b8" font-family="Arial, sans-serif" font-size="10">2024-01-01 · 技术</text>
  
  <rect x="440" y="140" width="360" height="200" fill="#ffffff" stroke="#e2e8f0" stroke-width="1" rx="6"/>
  <rect x="440" y="150" width="360" height="120" fill="#f1f5f9" rx="4"/>
  <text x="460" y="280" fill="#1e293b" font-family="Arial, sans-serif" font-size="16" font-weight="bold">优秀的用户体验</text>
  <text x="460" y="300" fill="#64748b" font-family="Arial, sans-serif" font-size="12">快速加载，SEO优化</text>
  <text x="460" y="320" fill="#94a3b8" font-family="Arial, sans-serif" font-size="10">2024-01-01 · 设计</text>
  
  <rect x="60" y="360" width="360" height="200" fill="#ffffff" stroke="#e2e8f0" stroke-width="1" rx="6"/>
  <rect x="60" y="370" width="360" height="120" fill="#f1f5f9" rx="4"/>
  <text x="80" y="500" fill="#1e293b" font-family="Arial, sans-serif" font-size="16" font-weight="bold">丰富的功能特性</text>
  <text x="80" y="520" fill="#64748b" font-family="Arial, sans-serif" font-size="12">标签云，归档，评论系统</text>
  <text x="80" y="540" fill="#94a3b8" font-family="Arial, sans-serif" font-size="10">2024-01-01 · 功能</text>
  
  <rect x="440" y="360" width="360" height="200" fill="#ffffff" stroke="#e2e8f0" stroke-width="1" rx="6"/>
  <rect x="440" y="370" width="360" height="120" fill="#f1f5f9" rx="4"/>
  <text x="460" y="500" fill="#1e293b" font-family="Arial, sans-serif" font-size="16" font-weight="bold">完美的移动体验</text>
  <text x="460" y="520" fill="#64748b" font-family="Arial, sans-serif" font-size="12">移动优先，触摸友好</text>
  <text x="460" y="540" fill="#94a3b8" font-family="Arial, sans-serif" font-size="10">2024-01-01 · 移动</text>
  
  <!-- Sidebar -->
  <rect x="880" y="120" width="280" height="720" fill="#ffffff" stroke="#e2e8f0" stroke-width="1" rx="8"/>
  
  <!-- Author card -->
  <rect x="900" y="140" width="240" height="120" fill="#f8fafc" stroke="#e2e8f0" stroke-width="1" rx="6"/>
  <circle cx="950" cy="180" r="20" fill="#2563eb"/>
  <text x="980" y="175" fill="#1e293b" font-family="Arial, sans-serif" font-size="14" font-weight="bold">作者简介</text>
  <text x="980" y="190" fill="#64748b" font-family="Arial, sans-serif" font-size="11">现代化主题开发者</text>
  <text x="920" y="240" fill="#94a3b8" font-family="Arial, sans-serif" font-size="10">文章: 10 | 评论: 25 | 访客: 100</text>
  
  <!-- Categories -->
  <rect x="900" y="280" width="240" height="100" fill="#f8fafc" stroke="#e2e8f0" stroke-width="1" rx="6"/>
  <text x="920" y="300" fill="#1e293b" font-family="Arial, sans-serif" font-size="14" font-weight="bold">分类目录</text>
  <text x="920" y="320" fill="#64748b" font-family="Arial, sans-serif" font-size="11">技术 (5)</text>
  <text x="920" y="335" fill="#64748b" font-family="Arial, sans-serif" font-size="11">设计 (3)</text>
  <text x="920" y="350" fill="#64748b" font-family="Arial, sans-serif" font-size="11">生活 (2)</text>
  
  <!-- Tags -->
  <rect x="900" y="400" width="240" height="80" fill="#f8fafc" stroke="#e2e8f0" stroke-width="1" rx="6"/>
  <text x="920" y="420" fill="#1e293b" font-family="Arial, sans-serif" font-size="14" font-weight="bold">标签云</text>
  <rect x="920" y="430" width="40" height="20" fill="#dbeafe" stroke="#2563eb" stroke-width="1" rx="10"/>
  <text x="940" y="443" text-anchor="middle" fill="#2563eb" font-family="Arial, sans-serif" font-size="9">PHP</text>
  <rect x="970" y="430" width="50" height="20" fill="#dbeafe" stroke="#2563eb" stroke-width="1" rx="10"/>
  <text x="995" y="443" text-anchor="middle" fill="#2563eb" font-family="Arial, sans-serif" font-size="9">设计</text>
  <rect x="1030" y="430" width="60" height="20" fill="#dbeafe" stroke="#2563eb" stroke-width="1" rx="10"/>
  <text x="1060" y="443" text-anchor="middle" fill="#2563eb" font-family="Arial, sans-serif" font-size="9">响应式</text>
  
  <!-- Footer -->
  <rect x="0" y="860" width="1200" height="40" fill="#1e293b"/>
  <text x="600" y="885" text-anchor="middle" fill="#94a3b8" font-family="Arial, sans-serif" font-size="12">© 2024 Ninecho Theme - 现代化响应式 Typecho 主题</text>
</svg>
