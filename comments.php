<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>

<section class="comments-section" id="comments">
    <div class="comments-container">
        
        <!-- 评论标题 -->
        <h3 class="comments-title">
            <?php $this->commentsNum(_t('暂无评论'), _t('仅有一条评论'), _t('已有 %d 条评论')); ?>
        </h3>
        
        <!-- 评论列表 -->
        <?php if ($this->allow('comment')): ?>
            <div class="comments-list" id="comments-list">
                <?php $this->comments()->to($comments); ?>
                <?php if ($comments->have()): ?>
                    <?php while($comments->next()): ?>
                        <div class="comment-item" id="comment-<?php $comments->theId(); ?>" itemscope itemtype="http://schema.org/Comment">
                            <div class="comment-avatar">
                                <img src="<?php echo Typecho_Common::gravatarUrl($comments->mail, 48, 'mm', 'g', false); ?>" 
                                     alt="<?php $comments->author(); ?>" 
                                     class="avatar-image"
                                     loading="lazy">
                            </div>
                            
                            <div class="comment-content">
                                <!-- 评论头部 -->
                                <div class="comment-header">
                                    <div class="comment-author-info">
                                        <span class="comment-author" itemprop="author" itemscope itemtype="http://schema.org/Person">
                                            <?php if ($comments->url): ?>
                                                <a href="<?php $comments->url(); ?>" target="_blank" rel="noopener noreferrer" itemprop="url">
                                                    <span itemprop="name"><?php $comments->author(); ?></span>
                                                </a>
                                            <?php else: ?>
                                                <span itemprop="name"><?php $comments->author(); ?></span>
                                            <?php endif; ?>
                                        </span>
                                        
                                        <?php if ($comments->authorId == $this->authorId): ?>
                                            <span class="comment-author-badge"><?php _e('作者'); ?></span>
                                        <?php endif; ?>
                                        
                                        <?php if ($comments->levels > 0): ?>
                                            <span class="comment-reply-to">
                                                <?php _e('回复给'); ?> 
                                                <?php $comments->levelsAlt(' ', ' '); ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    
                                    <div class="comment-meta">
                                        <time class="comment-date" datetime="<?php $comments->date('c'); ?>" itemprop="datePublished">
                                            <?php echo timeAgo($comments->created); ?>
                                        </time>
                                        
                                        <span class="comment-floor">
                                            #<?php echo $comments->sequence; ?>
                                        </span>
                                        
                                        <?php if ($comments->status == 'waiting'): ?>
                                            <span class="comment-awaiting"><?php _e('待审核'); ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                
                                <!-- 评论正文 -->
                                <div class="comment-body" itemprop="text">
                                    <?php $comments->content(); ?>
                                </div>
                                
                                <!-- 评论操作 -->
                                <div class="comment-actions">
                                    <?php if ($this->allow('comment')): ?>
                                        <button class="comment-reply-btn" 
                                                data-cid="<?php $comments->coid(); ?>" 
                                                data-author="<?php $comments->author(); ?>"
                                                aria-label="<?php _e('回复评论'); ?>">
                                            <svg class="icon" viewBox="0 0 24 24">
                                                <path d="M10,9V5L3,12L10,19V14.9C15,14.9 18.5,16.5 21,20C20,15 17,10 10,9Z" />
                                            </svg>
                                            <?php _e('回复'); ?>
                                        </button>
                                    <?php endif; ?>
                                    
                                    <button class="comment-like-btn" 
                                            data-cid="<?php $comments->coid(); ?>"
                                            aria-label="<?php _e('点赞评论'); ?>">
                                        <svg class="icon" viewBox="0 0 24 24">
                                            <path d="M23,10C23,8.89 22.1,8 21,8H14.68L15.64,3.43C15.66,3.33 15.67,3.22 15.67,3.11C15.67,2.7 15.5,2.32 15.23,2.05L14.17,1L7.59,7.58C7.22,7.95 7,8.45 7,9V19A2,2 0 0,0 9,21H18C18.83,21 19.54,20.5 19.84,19.78L22.86,12.73C22.95,12.5 23,12.26 23,12V10.08L23,10M1,21H5V9H1V21Z" />
                                        </svg>
                                        <span class="like-count">0</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 子评论 -->
                        <?php if ($comments->children): ?>
                            <div class="comment-children">
                                <?php $comments->threadedComments(); ?>
                            </div>
                        <?php endif; ?>
                    <?php endwhile; ?>
                    
                    <!-- 评论分页 -->
                    <?php $comments->pageNav('&laquo; 较早评论', '较新评论 &raquo;', 5, '...', array('wrapTag' => 'nav', 'wrapClass' => 'comment-pagination', 'itemTag' => '', 'textTag' => 'a', 'currentClass' => 'current', 'prevClass' => 'prev', 'nextClass' => 'next')); ?>
                    
                <?php else: ?>
                    <div class="no-comments">
                        <div class="no-comments-icon">
                            <svg viewBox="0 0 24 24">
                                <path d="M9,22A1,1 0 0,1 8,21V18H4A2,2 0 0,1 2,16V4C2,2.89 2.9,2 4,2H20A2,2 0 0,1 22,4V16A2,2 0 0,1 20,18H13.9L10.2,21.71C10,21.9 9.75,22 9.5,22V22H9Z" />
                            </svg>
                        </div>
                        <p><?php _e('还没有评论，快来抢沙发吧！'); ?></p>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
        
        <!-- 评论表单 -->
        <?php if($this->allow('comment')): ?>
            <div class="comment-form-container" id="comment-form-container">
                <h4 class="comment-form-title" id="response"><?php _e('发表评论'); ?></h4>
                
                <!-- 取消回复 -->
                <div class="cancel-comment-reply" id="cancel-comment-reply" style="display: none;">
                    <span class="reply-to-info"></span>
                    <button class="cancel-reply-btn" onclick="return TypechoComment.cancelReply();" aria-label="<?php _e('取消回复'); ?>">
                        <svg class="icon" viewBox="0 0 24 24">
                            <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                        </svg>
                        <?php _e('取消回复'); ?>
                    </button>
                </div>
                
                <form method="post" action="<?php $this->commentUrl() ?>" id="comment-form" class="comment-form" role="form">
                    <!-- 评论内容 -->
                    <div class="form-group comment-textarea-group">
                        <label for="textarea" class="form-label"><?php _e('评论内容'); ?> <span class="required">*</span></label>
                        <textarea rows="6" cols="50" name="text" id="textarea" class="form-control comment-textarea" placeholder="<?php _e('在这里输入你的评论...'); ?>" required><?php $this->remember('text'); ?></textarea>
                        <div class="textarea-tools">
                            <div class="emoji-picker">
                                <button type="button" class="emoji-btn" aria-label="<?php _e('插入表情'); ?>">
                                    <svg class="icon" viewBox="0 0 24 24">
                                        <path d="M12,2C6.47,2 2,6.47 2,12C2,17.53 6.47,22 12,22A10,10 0 0,0 22,12C22,6.47 17.5,2 12,2M12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4A8,8 0 0,1 20,12A8,8 0 0,1 12,20M17,9A1,1 0 0,1 16,10A1,1 0 0,1 15,9A1,1 0 0,1 16,8A1,1 0 0,1 17,9M9,9A1,1 0 0,1 8,10A1,1 0 0,1 7,9A1,1 0 0,1 8,8A1,1 0 0,1 9,9M12,17.5C14.33,17.5 16.31,16.04 17.11,14H6.89C7.69,16.04 9.67,17.5 12,17.5Z" />
                                    </svg>
                                </button>
                                <div class="emoji-panel">
                                    <div class="emoji-list">
                                        <span class="emoji-item">😀</span>
                                        <span class="emoji-item">😃</span>
                                        <span class="emoji-item">😄</span>
                                        <span class="emoji-item">😁</span>
                                        <span class="emoji-item">😆</span>
                                        <span class="emoji-item">😅</span>
                                        <span class="emoji-item">😂</span>
                                        <span class="emoji-item">🤣</span>
                                        <span class="emoji-item">😊</span>
                                        <span class="emoji-item">😇</span>
                                        <span class="emoji-item">🙂</span>
                                        <span class="emoji-item">🙃</span>
                                        <span class="emoji-item">😉</span>
                                        <span class="emoji-item">😌</span>
                                        <span class="emoji-item">😍</span>
                                        <span class="emoji-item">🥰</span>
                                        <span class="emoji-item">😘</span>
                                        <span class="emoji-item">😗</span>
                                        <span class="emoji-item">😙</span>
                                        <span class="emoji-item">😚</span>
                                        <span class="emoji-item">😋</span>
                                        <span class="emoji-item">😛</span>
                                        <span class="emoji-item">😝</span>
                                        <span class="emoji-item">😜</span>
                                        <span class="emoji-item">🤪</span>
                                        <span class="emoji-item">🤨</span>
                                        <span class="emoji-item">🧐</span>
                                        <span class="emoji-item">🤓</span>
                                        <span class="emoji-item">😎</span>
                                        <span class="emoji-item">🤩</span>
                                        <span class="emoji-item">🥳</span>
                                        <span class="emoji-item">😏</span>
                                        <span class="emoji-item">😒</span>
                                        <span class="emoji-item">😞</span>
                                        <span class="emoji-item">😔</span>
                                        <span class="emoji-item">😟</span>
                                        <span class="emoji-item">😕</span>
                                        <span class="emoji-item">🙁</span>
                                        <span class="emoji-item">☹️</span>
                                        <span class="emoji-item">😣</span>
                                        <span class="emoji-item">😖</span>
                                        <span class="emoji-item">😫</span>
                                        <span class="emoji-item">😩</span>
                                        <span class="emoji-item">🥺</span>
                                        <span class="emoji-item">😢</span>
                                        <span class="emoji-item">😭</span>
                                        <span class="emoji-item">😤</span>
                                        <span class="emoji-item">😠</span>
                                        <span class="emoji-item">😡</span>
                                        <span class="emoji-item">🤬</span>
                                        <span class="emoji-item">🤯</span>
                                        <span class="emoji-item">😳</span>
                                        <span class="emoji-item">🥵</span>
                                        <span class="emoji-item">🥶</span>
                                        <span class="emoji-item">😱</span>
                                        <span class="emoji-item">😨</span>
                                        <span class="emoji-item">😰</span>
                                        <span class="emoji-item">😥</span>
                                        <span class="emoji-item">😓</span>
                                        <span class="emoji-item">🤗</span>
                                        <span class="emoji-item">🤔</span>
                                        <span class="emoji-item">🤭</span>
                                        <span class="emoji-item">🤫</span>
                                        <span class="emoji-item">🤥</span>
                                        <span class="emoji-item">😶</span>
                                        <span class="emoji-item">😐</span>
                                        <span class="emoji-item">😑</span>
                                        <span class="emoji-item">😬</span>
                                        <span class="emoji-item">🙄</span>
                                        <span class="emoji-item">😯</span>
                                        <span class="emoji-item">😦</span>
                                        <span class="emoji-item">😧</span>
                                        <span class="emoji-item">😮</span>
                                        <span class="emoji-item">😲</span>
                                        <span class="emoji-item">🥱</span>
                                        <span class="emoji-item">😴</span>
                                        <span class="emoji-item">🤤</span>
                                        <span class="emoji-item">😪</span>
                                        <span class="emoji-item">😵</span>
                                        <span class="emoji-item">🤐</span>
                                        <span class="emoji-item">🥴</span>
                                        <span class="emoji-item">🤢</span>
                                        <span class="emoji-item">🤮</span>
                                        <span class="emoji-item">🤧</span>
                                        <span class="emoji-item">😷</span>
                                        <span class="emoji-item">🤒</span>
                                        <span class="emoji-item">🤕</span>
                                        <span class="emoji-item">🤑</span>
                                        <span class="emoji-item">🤠</span>
                                        <span class="emoji-item">😈</span>
                                        <span class="emoji-item">👿</span>
                                        <span class="emoji-item">👹</span>
                                        <span class="emoji-item">👺</span>
                                        <span class="emoji-item">🤡</span>
                                        <span class="emoji-item">💩</span>
                                        <span class="emoji-item">👻</span>
                                        <span class="emoji-item">💀</span>
                                        <span class="emoji-item">☠️</span>
                                        <span class="emoji-item">👽</span>
                                        <span class="emoji-item">👾</span>
                                        <span class="emoji-item">🤖</span>
                                        <span class="emoji-item">🎃</span>
                                        <span class="emoji-item">😺</span>
                                        <span class="emoji-item">😸</span>
                                        <span class="emoji-item">😹</span>
                                        <span class="emoji-item">😻</span>
                                        <span class="emoji-item">😼</span>
                                        <span class="emoji-item">😽</span>
                                        <span class="emoji-item">🙀</span>
                                        <span class="emoji-item">😿</span>
                                        <span class="emoji-item">😾</span>
                                    </div>
                                </div>
                            </div>
                            <div class="char-count">
                                <span id="char-count">0</span>/500
                            </div>
                        </div>
                    </div>
                    
                    <!-- 用户信息 -->
                    <?php if(!$this->user->hasLogin()): ?>
                        <div class="form-row">
                            <div class="form-group">
                                <label for="author" class="form-label"><?php _e('姓名'); ?> <span class="required">*</span></label>
                                <input type="text" name="author" id="author" class="form-control" value="<?php $this->remember('author'); ?>" placeholder="<?php _e('请输入您的姓名'); ?>" required />
                            </div>
                            
                            <div class="form-group">
                                <label for="mail" class="form-label"><?php _e('邮箱'); ?> <span class="required">*</span></label>
                                <input type="email" name="mail" id="mail" class="form-control" value="<?php $this->remember('mail'); ?>" placeholder="<?php _e('请输入您的邮箱'); ?>" required />
                            </div>
                            
                            <div class="form-group">
                                <label for="url" class="form-label"><?php _e('网站'); ?></label>
                                <input type="url" name="url" id="url" class="form-control" value="<?php $this->remember('url'); ?>" placeholder="<?php _e('请输入您的网站地址'); ?>" />
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="logged-in-info">
                            <span><?php _e('当前登录用户'); ?>: <strong><?php $this->user->screenName(); ?></strong></span>
                            <a href="<?php $this->options->logoutUrl(); ?>" class="logout-link"><?php _e('退出登录'); ?></a>
                        </div>
                    <?php endif; ?>
                    
                    <!-- 提交按钮 -->
                    <div class="form-actions">
                        <button type="submit" class="submit-btn" id="submit-btn">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M2,21L23,12L2,3V10L17,12L2,14V21Z" />
                            </svg>
                            <?php _e('发表评论'); ?>
                        </button>
                        
                        <div class="comment-policy">
                            <small><?php _e('评论将在审核后显示，请文明发言。'); ?></small>
                        </div>
                    </div>
                </form>
            </div>
        <?php else: ?>
            <div class="comments-closed">
                <p><?php _e('评论已关闭'); ?></p>
            </div>
        <?php endif; ?>
    </div>
</section>
