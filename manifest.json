{"name": "Ninecho Blog", "short_name": "<PERSON><PERSON>", "description": "A modern, responsive Typecho blog theme with PWA support - Ninecho", "start_url": "/", "display": "standalone", "background_color": "#ffffff", "theme_color": "#2563eb", "orientation": "portrait-primary", "scope": "/", "lang": "zh-CN", "dir": "ltr", "categories": ["blog", "news", "lifestyle"], "icons": [{"src": "/assets/images/icon-72.png", "sizes": "72x72", "type": "image/png", "purpose": "any"}, {"src": "/assets/images/icon-96.png", "sizes": "96x96", "type": "image/png", "purpose": "any"}, {"src": "/assets/images/icon-128.png", "sizes": "128x128", "type": "image/png", "purpose": "any"}, {"src": "/assets/images/icon-144.png", "sizes": "144x144", "type": "image/png", "purpose": "any"}, {"src": "/assets/images/icon-152.png", "sizes": "152x152", "type": "image/png", "purpose": "any"}, {"src": "/assets/images/icon-192.png", "sizes": "192x192", "type": "image/png", "purpose": "any maskable"}, {"src": "/assets/images/icon-384.png", "sizes": "384x384", "type": "image/png", "purpose": "any"}, {"src": "/assets/images/icon-512.png", "sizes": "512x512", "type": "image/png", "purpose": "any maskable"}], "screenshots": [{"src": "/assets/images/screenshot-desktop.png", "sizes": "1280x720", "type": "image/png", "form_factor": "wide", "label": "Desktop view of the blog"}, {"src": "/assets/images/screenshot-mobile.png", "sizes": "390x844", "type": "image/png", "form_factor": "narrow", "label": "Mobile view of the blog"}], "shortcuts": [{"name": "Write Post", "short_name": "Write", "description": "Create a new blog post", "url": "/admin/write-post.php", "icons": [{"src": "/assets/images/shortcut-write.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Archives", "short_name": "Archives", "description": "Browse post archives", "url": "/archives.html", "icons": [{"src": "/assets/images/shortcut-archive.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Dashboard", "short_name": "Admin", "description": "Admin dashboard", "url": "/admin/", "icons": [{"src": "/assets/images/shortcut-admin.png", "sizes": "96x96", "type": "image/png"}]}], "share_target": {"action": "/share-target", "method": "POST", "enctype": "multipart/form-data", "params": {"title": "title", "text": "text", "url": "url", "files": [{"name": "files", "accept": ["image/*", "text/*"]}]}}, "protocol_handlers": [{"protocol": "web+blog", "url": "/share?url=%s"}], "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "navigate-existing"}, "handle_links": "preferred", "prefer_related_applications": false, "related_applications": [{"platform": "webapp", "url": "https://example.com/manifest.json"}]}