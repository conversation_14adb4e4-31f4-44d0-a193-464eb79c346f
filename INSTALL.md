# Ninecho 主题安装指南

## 🚀 快速安装

### 1. 下载主题
将主题文件夹上传到 Typecho 的 `usr/themes/` 目录下。

### 2. 启用主题
1. 登录 Typecho 后台
2. 进入「外观」→「使用外观」
3. 找到 Ninecho 主题，点击「启用」

### 3. 配置主题
1. 进入「外观」→「设置外观」
2. 根据需要配置各项设置

## 🔧 解决常见问题

### 问题：Call to undefined method Typecho\Db\Query::onDuplicate()

**原因**：主题使用了不兼容的数据库方法

**解决方案**：
1. 确保使用最新版本的主题文件
2. 检查 PHP 版本（推荐 7.4+）
3. 检查 Typecho 版本（推荐 1.1+）

### 问题：主题后台选项消失

**原因**：主题配置函数出现错误

**解决方案**：
1. 检查 `functions.php` 文件是否完整
2. 确保文件权限正确（644）
3. 查看服务器错误日志

### 问题：页面显示空白

**原因**：PHP 语法错误或缺少必要文件

**解决方案**：
1. 检查所有主题文件是否完整上传
2. 启用 PHP 错误显示查看具体错误
3. 确保服务器支持所需的 PHP 扩展

## 📄 创建独立页面

### 文章分类页面
1. 创建新页面，标题：「文章分类」
2. 添加自定义字段：
   - 字段名：`template`
   - 字段值：`categories`

### 文章归档页面
1. 创建新页面，标题：「文章归档」
2. 添加自定义字段：
   - 字段名：`template`
   - 字段值：`archives`

### 文章标签页面
1. 创建新页面，标题：「文章标签」
2. 添加自定义字段：
   - 字段名：`template`
   - 字段值：`tags`

### 友情链接页面
1. 创建新页面，标题：「友情链接」
2. 添加自定义字段：
   - 字段名：`template`
   - 字段值：`links`

## ⚙️ 系统要求

- **PHP**: 7.0 或更高版本（推荐 7.4+）
- **Typecho**: 1.1 或更高版本
- **MySQL**: 5.6 或更高版本
- **Web服务器**: Apache 或 Nginx

## 📞 技术支持

如果遇到问题：
1. 检查服务器错误日志
2. 确认系统要求
3. 查看主题文档
4. 联系技术支持

## 🔄 更新主题

1. 备份当前主题设置
2. 下载最新版本
3. 替换主题文件
4. 重新配置设置（如需要）

---

**注意**：安装前请备份您的网站数据！
