<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>
<?php
/**
 * 文章分类
 *
 * @package Ninecho
 */

$this->need('header.php');
?>

<main class="main-content">
    <div class="container">
        <div class="content-wrapper">
            <div class="page-main">
                <!-- Breadcrumb -->
                <nav class="breadcrumb" aria-label="面包屑导航">
                    <ol class="breadcrumb-list">
                        <li class="breadcrumb-item">
                            <a href="<?php $this->options->siteUrl(); ?>">首页</a>
                        </li>
                        <li class="breadcrumb-item">
                            <?php $this->title() ?>
                        </li>
                    </ol>
                </nav>

                <!-- Page Header -->
                <header class="page-header">
                    <h1 class="page-title">
                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
                        </svg>
                        <?php $this->title() ?>
                    </h1>
                    <?php if ($this->fields->subtitle): ?>
                        <p class="page-subtitle"><?php $this->fields->subtitle(); ?></p>
                    <?php endif; ?>
                    <div class="page-meta">
                        <span class="categories-count">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                <polyline points="14,2 14,8 20,8"></polyline>
                            </svg>
                            <?php 
                            $this->widget('Widget_Metas_Category_List')->to($categoryCount);
                            $count = 0;
                            while($categoryCount->next()) { $count++; }
                            echo "共 {$count} 个分类";
                            ?>
                        </span>
                        <time datetime="<?php $this->date('c'); ?>" class="page-date">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12,6 12,12 16,14"></polyline>
                            </svg>
                            最后更新：<?php $this->date('Y-m-d'); ?>
                        </time>
                    </div>
                </header>

                <!-- Page Content -->
                <article class="page-content">
                    <div class="page-body">
                        <?php $this->content(); ?>
                    </div>

                    <!-- Categories Grid -->
                    <div class="categories-section">
                        <div class="categories-grid">
                            <?php $this->widget('Widget_Metas_Category_List')->to($categories); ?>
                            <?php while($categories->next()): ?>
                                <div class="category-card">
                                    <div class="category-icon">
                                        <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                            <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"></path>
                                        </svg>
                                    </div>
                                    <div class="category-info">
                                        <h3 class="category-name">
                                            <a href="<?php $categories->permalink(); ?>" title="查看分类：<?php $categories->name(); ?>">
                                                <?php $categories->name(); ?>
                                            </a>
                                        </h3>
                                        <?php if ($categories->description): ?>
                                            <p class="category-description">
                                                <?php echo $categories->description; ?>
                                            </p>
                                        <?php endif; ?>
                                        <div class="category-meta">
                                            <span class="category-count">
                                                <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                                                    <polyline points="14,2 14,8 20,8"></polyline>
                                                    <line x1="16" y1="13" x2="8" y2="13"></line>
                                                    <line x1="16" y1="17" x2="8" y2="17"></line>
                                                    <polyline points="10,9 9,9 8,9"></polyline>
                                                </svg>
                                                <?php $categories->count(); ?> 篇文章
                                            </span>
                                        </div>
                                    </div>
                                    <div class="category-actions">
                                        <a href="<?php $categories->permalink(); ?>" class="view-category-btn" title="查看分类">
                                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                                <circle cx="12" cy="12" r="3"></circle>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    </div>

                    <!-- Category Statistics -->
                    <div class="category-stats">
                        <h2 class="stats-title">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <line x1="18" y1="20" x2="18" y2="10"></line>
                                <line x1="12" y1="20" x2="12" y2="4"></line>
                                <line x1="6" y1="20" x2="6" y2="14"></line>
                            </svg>
                            分类统计
                        </h2>
                        <div class="stats-grid">
                            <?php 
                            $this->widget('Widget_Metas_Category_List')->to($statsCategories);
                            $totalPosts = 0;
                            $categoriesData = array();
                            while($statsCategories->next()) {
                                $totalPosts += $statsCategories->count;
                                $categoriesData[] = array(
                                    'name' => $statsCategories->name,
                                    'count' => $statsCategories->count,
                                    'permalink' => $statsCategories->permalink
                                );
                            }
                            
                            // 按文章数量排序
                            usort($categoriesData, function($a, $b) {
                                return $b['count'] - $a['count'];
                            });
                            
                            foreach ($categoriesData as $index => $category):
                                $percentage = $totalPosts > 0 ? round(($category['count'] / $totalPosts) * 100, 1) : 0;
                            ?>
                                <div class="stat-item">
                                    <div class="stat-header">
                                        <span class="stat-rank">#<?php echo $index + 1; ?></span>
                                        <a href="<?php echo $category['permalink']; ?>" class="stat-name">
                                            <?php echo $category['name']; ?>
                                        </a>
                                    </div>
                                    <div class="stat-bar">
                                        <div class="stat-progress" style="width: <?php echo $percentage; ?>%"></div>
                                    </div>
                                    <div class="stat-info">
                                        <span class="stat-count"><?php echo $category['count']; ?> 篇</span>
                                        <span class="stat-percentage"><?php echo $percentage; ?>%</span>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </article>

                <!-- Comments -->
                <?php $this->need('comments.php'); ?>
            </div>
            
            <!-- Sidebar -->
            <?php $this->need('sidebar.php'); ?>
        </div>
    </div>
</main>

<?php $this->need('footer.php'); ?>
