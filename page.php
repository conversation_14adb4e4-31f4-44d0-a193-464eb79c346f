<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>
<?php
// 检查是否有自定义模板
$template = $this->fields->template;
if ($template) {
    $templateFile = 'page-' . $template . '.php';
    if (file_exists(__DIR__ . '/' . $templateFile)) {
        include $templateFile;
        return;
    }
}

$this->need('header.php');
?>

<main class="main-content">
    <div class="container">
        <div class="content-wrapper">
            <div class="page-main">
                <!-- Breadcrumb -->
                <nav class="breadcrumb" aria-label="面包屑导航">
                    <ol class="breadcrumb-list">
                        <li class="breadcrumb-item">
                            <a href="<?php $this->options->siteUrl(); ?>">首页</a>
                        </li>
                        <li class="breadcrumb-item">
                            <?php $this->title() ?>
                        </li>
                    </ol>
                </nav>

                <!-- Page Header -->
                <header class="page-header">
                    <h1 class="page-title"><?php $this->title() ?></h1>
                    <?php if ($this->fields->subtitle): ?>
                        <p class="page-subtitle"><?php $this->fields->subtitle(); ?></p>
                    <?php endif; ?>
                    <div class="page-meta">
                        <time datetime="<?php $this->date('c'); ?>" class="page-date">
                            <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                                <circle cx="12" cy="12" r="10"></circle>
                                <polyline points="12,6 12,12 16,14"></polyline>
                            </svg>
                            最后更新：<?php $this->date('Y-m-d'); ?>
                        </time>
                    </div>
                </header>

                <!-- Page Content -->
                <article class="page-content">
                    <div class="page-body">
                        <?php $this->content(); ?>
                    </div>
                </article>

                <!-- Comments -->
                <?php $this->need('comments.php'); ?>
            </div>

            <!-- Sidebar -->
            <?php $this->need('sidebar.php'); ?>
        </div>
    </div>
</main>

<?php $this->need('footer.php'); ?>
