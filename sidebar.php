<?php if (!defined('__TYPECHO_ROOT_DIR__')) exit; ?>

<aside class="sidebar" role="complementary" itemscope itemtype="http://schema.org/WPSideBar">
    <div class="sidebar-content">
        
        <!-- 作者简介卡片 -->
        <div class="widget author-widget">
            <div class="widget-content">
                <div class="author-card">
                    <?php if ($this->options->authorAvatar): ?>
                        <div class="author-avatar">
                            <img src="<?php echo $this->options->authorAvatar; ?>" alt="<?php $this->author(); ?>" class="avatar-image">
                        </div>
                    <?php endif; ?>
                    
                    <div class="author-info">
                        <h3 class="author-name"><?php $this->options->title(); ?></h3>
                        <p class="author-description"><?php echo $this->options->description(); ?></p>
                        
                        <!-- 作者统计 -->
                        <div class="author-stats">
                            <?php Typecho_Widget::widget('Widget_Stat')->to($stat); ?>
                            <div class="stat-item">
                                <span class="stat-number"><?php echo $stat->publishedPostsNum; ?></span>
                                <span class="stat-label"><?php _e('文章'); ?></span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number"><?php echo $stat->publishedCommentsNum; ?></span>
                                <span class="stat-label"><?php _e('评论'); ?></span>
                            </div>
                            <div class="stat-item">
                                <span class="stat-number"><?php echo getVisitorCount(); ?></span>
                                <span class="stat-label"><?php _e('访客'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 搜索框 -->
        <div class="widget search-widget">
            <h3 class="widget-title"><?php _e('搜索'); ?></h3>
            <div class="widget-content">
                <form class="search-form" method="post" action="<?php $this->options->siteUrl(); ?>" role="search">
                    <div class="search-input-group">
                        <input type="search" name="s" placeholder="<?php _e('输入关键词...'); ?>" value="<?php $this->archiveSlug(); ?>" autocomplete="off" required>
                        <button type="submit" class="search-submit" aria-label="<?php _e('搜索'); ?>">
                            <svg class="icon" viewBox="0 0 24 24">
                                <path d="M9.5,3A6.5,6.5 0 0,1 16,9.5C16,11.11 15.41,12.59 14.44,13.73L14.71,14H15.5L20.5,19L19,20.5L14,15.5V14.71L13.73,14.44C12.59,15.41 11.11,16 9.5,16A6.5,6.5 0 0,1 3,9.5A6.5,6.5 0 0,1 9.5,3M9.5,5C7,5 5,7 5,9.5C5,12 7,14 9.5,14C12,14 14,12 14,9.5C14,7 12,5 9.5,5Z" />
                            </svg>
                        </button>
                    </div>
                </form>
            </div>
        </div>
        
        <!-- 最新文章 -->
        <div class="widget recent-posts-widget">
            <h3 class="widget-title"><?php _e('最新文章'); ?></h3>
            <div class="widget-content">
                <ul class="recent-posts-list">
                    <?php $this->widget('Widget_Contents_Post_Recent', 'pageSize=5')->to($recent); ?>
                    <?php while($recent->next()): ?>
                        <li class="recent-post-item">
                            <a href="<?php $recent->permalink(); ?>" class="recent-post-link">
                                <?php if ($recent->fields->thumbnail): ?>
                                    <div class="recent-post-thumbnail">
                                        <img src="<?php echo $recent->fields->thumbnail; ?>" alt="<?php $recent->title(); ?>" loading="lazy">
                                    </div>
                                <?php endif; ?>
                                <div class="recent-post-content">
                                    <h4 class="recent-post-title"><?php $recent->title(); ?></h4>
                                    <time class="recent-post-date" datetime="<?php $recent->date('c'); ?>">
                                        <?php $recent->date('m-d'); ?>
                                    </time>
                                </div>
                            </a>
                        </li>
                    <?php endwhile; ?>
                </ul>
            </div>
        </div>
        
        <!-- 分类目录 -->
        <div class="widget categories-widget">
            <h3 class="widget-title"><?php _e('分类目录'); ?></h3>
            <div class="widget-content">
                <ul class="categories-list">
                    <?php $this->widget('Widget_Metas_Category_List')->to($categories); ?>
                    <?php while($categories->next()): ?>
                        <li class="category-item">
                            <a href="<?php $categories->permalink(); ?>" class="category-link">
                                <span class="category-name"><?php $categories->name(); ?></span>
                                <span class="category-count"><?php $categories->count(); ?></span>
                            </a>
                        </li>
                    <?php endwhile; ?>
                </ul>
            </div>
        </div>
        
        <!-- 标签云 -->
        <div class="widget tags-widget">
            <h3 class="widget-title"><?php _e('标签云'); ?></h3>
            <div class="widget-content">
                <div class="tags-cloud">
                    <?php $this->widget('Widget_Metas_Tag_Cloud', 'sort=count&ignoreZeroCount=1&desc=1&limit=30')->to($tags); ?>
                    <?php if($tags->have()): ?>
                        <?php while($tags->next()): ?>
                            <a href="<?php $tags->permalink(); ?>" class="tag-link" style="font-size: <?php echo 12 + min($tags->count, 10); ?>px;">
                                <?php $tags->name(); ?>
                            </a>
                        <?php endwhile; ?>
                    <?php else: ?>
                        <p class="no-tags"><?php _e('暂无标签'); ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <!-- 最新评论 -->
        <div class="widget recent-comments-widget">
            <h3 class="widget-title"><?php _e('最新评论'); ?></h3>
            <div class="widget-content">
                <ul class="recent-comments-list">
                    <?php $this->widget('Widget_Comments_Recent', 'pageSize=5&ignoreAuthor=true')->to($comments); ?>
                    <?php while($comments->next()): ?>
                        <li class="recent-comment-item">
                            <div class="comment-avatar">
                                <img src="<?php echo Typecho_Common::gravatarUrl($comments->mail, 32, 'mm', 'g', false); ?>" alt="<?php $comments->author(false); ?>" loading="lazy">
                            </div>
                            <div class="comment-content">
                                <div class="comment-author">
                                    <span class="author-name"><?php $comments->author(false); ?></span>
                                    <time class="comment-date" datetime="<?php $comments->date('c'); ?>">
                                        <?php echo timeAgo($comments->created); ?>
                                    </time>
                                </div>
                                <div class="comment-text">
                                    <a href="<?php $comments->permalink(); ?>" title="<?php $comments->title(); ?>">
                                        <?php echo mb_substr(strip_tags($comments->content), 0, 50, 'UTF-8'); ?>...
                                    </a>
                                </div>
                            </div>
                        </li>
                    <?php endwhile; ?>
                </ul>
            </div>
        </div>
        
        <!-- 归档 -->
        <div class="widget archives-widget">
            <h3 class="widget-title"><?php _e('文章归档'); ?></h3>
            <div class="widget-content">
                <ul class="archives-list">
                    <?php $this->widget('Widget_Contents_Post_Date', 'type=month&format=Y年m月')->to($archives); ?>
                    <?php while($archives->next()): ?>
                        <li class="archive-item">
                            <a href="<?php $archives->permalink(); ?>" class="archive-link">
                                <span class="archive-date"><?php $archives->date(); ?></span>
                                <span class="archive-count"><?php $archives->count(); ?></span>
                            </a>
                        </li>
                    <?php endwhile; ?>
                </ul>
            </div>
        </div>
        
        <!-- 友情链接 -->
        <?php if ($this->options->friendLinks): ?>
            <div class="widget friends-widget">
                <h3 class="widget-title"><?php _e('友情链接'); ?></h3>
                <div class="widget-content">
                    <ul class="friends-list">
                        <?php 
                        $friendLinks = explode("\n", $this->options->friendLinks);
                        foreach ($friendLinks as $link) {
                            $linkData = explode('|', trim($link));
                            if (count($linkData) >= 2) {
                                echo '<li class="friend-item">';
                                echo '<a href="' . trim($linkData[1]) . '" target="_blank" rel="noopener noreferrer" class="friend-link">';
                                echo '<span class="friend-name">' . trim($linkData[0]) . '</span>';
                                if (isset($linkData[2])) {
                                    echo '<span class="friend-desc">' . trim($linkData[2]) . '</span>';
                                }
                                echo '</a>';
                                echo '</li>';
                            }
                        }
                        ?>
                    </ul>
                </div>
            </div>
        <?php endif; ?>
        
        <!-- 自定义小工具 -->
        <?php if ($this->options->customWidget): ?>
            <div class="widget custom-widget">
                <div class="widget-content">
                    <?php echo $this->options->customWidget; ?>
                </div>
            </div>
        <?php endif; ?>
        
    </div>
</aside>
